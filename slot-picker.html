<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Valorant Random Slot Picker | Choose Your Players</title>
    <meta name="description" content="Free Valorant random slot picker tool. Randomly select 10 players from 22 available slots for custom matches. Created by <PERSON><PERSON><PERSON>.">
    <meta name="keywords" content="valorant, valorant slots, random slot picker, valorant random player, valorant tool, gaming tool, slot selector, valorant slot randomizer">
    <meta name="author" content="Shushie">
    <meta property="og:title" content="Valorant Random Slot Picker">
    <meta property="og:description" content="Free tool to randomly select 10 players from 22 slots for your Valorant custom matches.">
    <meta property="og:type" content="website">
    <meta name="robots" content="index, follow">
    <link rel="stylesheet" href="slot-picker-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Oswald:wght@400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <h1 class="valorant-title">VALORANT</h1>
            </div>
            <h1>RANDOM SLOT PICKER</h1>
        </div>

        <div class="main-content">
            <div class="lobby-container">
            <!-- Main Game Slots -->
            <div class="game-slots">
                <!-- Attackers Section -->
                <div class="team-section attackers">
                    <h2 class="team-title">ATTACKERS</h2>
                    <div class="slots-grid">
                        <div class="slot" data-slot="1">
                            <div class="slot-content">
                                <div class="player-name">Player 1</div>
                                <div class="slot-status">Ready</div>
                            </div>
                        </div>
                        <div class="slot" data-slot="2">
                            <div class="slot-content">
                                <div class="player-name">Player 2</div>
                                <div class="slot-status">Ready</div>
                            </div>
                        </div>
                        <div class="slot" data-slot="3">
                            <div class="slot-content">
                                <div class="player-name">Player 3</div>
                                <div class="slot-status">Ready</div>
                            </div>
                        </div>
                        <div class="slot" data-slot="4">
                            <div class="slot-content">
                                <div class="player-name">Player 4</div>
                                <div class="slot-status">Ready</div>
                            </div>
                        </div>
                        <div class="slot" data-slot="5">
                            <div class="slot-content">
                                <div class="player-name">Player 5</div>
                                <div class="slot-status">Ready</div>
                            </div>
                        </div>
                    </div>

                    <!-- Attackers Coach -->
                    <div class="coach-section">
                        <h3 class="coach-title">ATTACKERS COACH</h3>
                        <div class="slot" data-slot="21">
                            <div class="slot-content">
                                <div class="player-name">Player 21</div>
                                <div class="slot-status">Ready</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Defenders Section -->
                <div class="team-section defenders">
                    <h2 class="team-title">DEFENDERS</h2>
                    <div class="slots-grid">
                        <div class="slot" data-slot="6">
                            <div class="slot-content">
                                <div class="player-name">Player 6</div>
                                <div class="slot-status">Ready</div>
                            </div>
                        </div>
                        <div class="slot" data-slot="7">
                            <div class="slot-content">
                                <div class="player-name">Player 7</div>
                                <div class="slot-status">Ready</div>
                            </div>
                        </div>
                        <div class="slot" data-slot="8">
                            <div class="slot-content">
                                <div class="player-name">Player 8</div>
                                <div class="slot-status">Ready</div>
                            </div>
                        </div>
                        <div class="slot" data-slot="9">
                            <div class="slot-content">
                                <div class="player-name">Player 9</div>
                                <div class="slot-status">Ready</div>
                            </div>
                        </div>
                        <div class="slot" data-slot="10">
                            <div class="slot-content">
                                <div class="player-name">Player 10</div>
                                <div class="slot-status">Ready</div>
                            </div>
                        </div>
                    </div>

                    <!-- Defenders Coach -->
                    <div class="coach-section">
                        <h3 class="coach-title">DEFENDERS COACH</h3>
                        <div class="slot" data-slot="22">
                            <div class="slot-content">
                                <div class="player-name">Player 22</div>
                                <div class="slot-status">Ready</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Observers Section -->
                <div class="team-section observers">
                    <h2 class="team-title">OBSERVERS</h2>
                    <div class="observers-grid">
                        <div class="slot" data-slot="11">
                            <div class="slot-content">
                                <div class="player-name">Player 11</div>
                                <div class="slot-status">Ready</div>
                            </div>
                        </div>
                        <div class="slot" data-slot="12">
                            <div class="slot-content">
                                <div class="player-name">Player 12</div>
                                <div class="slot-status">Ready</div>
                            </div>
                        </div>
                        <div class="slot" data-slot="13">
                            <div class="slot-content">
                                <div class="player-name">Player 13</div>
                                <div class="slot-status">Ready</div>
                            </div>
                        </div>
                        <div class="slot" data-slot="14">
                            <div class="slot-content">
                                <div class="player-name">Player 14</div>
                                <div class="slot-status">Ready</div>
                            </div>
                        </div>
                        <div class="slot" data-slot="15">
                            <div class="slot-content">
                                <div class="player-name">Player 15</div>
                                <div class="slot-status">Ready</div>
                            </div>
                        </div>
                        <div class="slot" data-slot="16">
                            <div class="slot-content">
                                <div class="player-name">Player 16</div>
                                <div class="slot-status">Ready</div>
                            </div>
                        </div>
                        <div class="slot" data-slot="17">
                            <div class="slot-content">
                                <div class="player-name">Player 17</div>
                                <div class="slot-status">Ready</div>
                            </div>
                        </div>
                        <div class="slot" data-slot="18">
                            <div class="slot-content">
                                <div class="player-name">Player 18</div>
                                <div class="slot-status">Ready</div>
                            </div>
                        </div>
                        <div class="slot" data-slot="19">
                            <div class="slot-content">
                                <div class="player-name">Player 19</div>
                                <div class="slot-status">Ready</div>
                            </div>
                        </div>
                        <div class="slot" data-slot="20">
                            <div class="slot-content">
                                <div class="player-name">Player 20</div>
                                <div class="slot-status">Ready</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </div>

            <div class="sidebar">
                <div class="controls-section">
                    <h3 class="section-title">CONTROLS</h3>
                    <div class="controls">
                        <button id="pick-random-btn" class="val-btn">PICK RANDOM 10 PLAYERS</button>
                        <button id="reset-btn" class="val-btn secondary">RESET SELECTION</button>
                        <button id="clear-afk-btn" class="val-btn secondary">CLEAR AFK</button>
                    </div>

                    <div class="selection-info">
                        <p id="selection-count">Select 10 players to start the match</p>
                    </div>
                </div>

                <div class="shortcuts-section">
                    <h3 class="section-title">SHORTCUTS</h3>
                    <div class="shortcuts-list">
                        <div class="shortcut-item">
                            <span class="shortcut-key">SPACE</span>
                            <span class="shortcut-desc">Random Pick</span>
                        </div>
                        <div class="shortcut-item">
                            <span class="shortcut-key">ESC</span>
                            <span class="shortcut-desc">Reset Selection</span>
                        </div>
                        <div class="shortcut-item">
                            <span class="shortcut-key">ALT+Click</span>
                            <span class="shortcut-desc">Toggle AFK</span>
                        </div>
                    </div>
                </div>

                <div class="info-section">
                    <div class="links">
                        <a href="https://valormp.onrender.com" class="switch-version" target="_blank">Try the Map Picker!</a>
                    </div>

                    <footer class="creator-info">
                        <p>Created by <a href="https://www.youtube.com/@Shushie_valorant" target="_blank" rel="noopener noreferrer">Shushie</a></p>
                        <a href="https://www.youtube.com/@Shushie_valorant" target="_blank" rel="noopener noreferrer" class="youtube-link">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/>
                            </svg>
                            Subscribe on YouTube
                        </a>
                    </footer>
                </div>
            </div>
        </div>
    </div>

    <script src="slot-picker-script.js"></script>
</body>
</html>