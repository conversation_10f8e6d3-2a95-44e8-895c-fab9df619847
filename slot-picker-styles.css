:root {
    --valorant-red: #ff4655;
    --valorant-dark: #111;
    --valorant-light: #ece8e1;
    --valorant-blue: #0f1923;
    --valorant-teal: #1e3a5f;
    --valorant-selected: #00d4aa;
    --valorant-afk: #666;
    --valorant-afk-bg: rgba(102, 102, 102, 0.2);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Oswald', sans-serif;
    background-color: var(--valorant-blue);
    color: var(--valorant-light);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    position: relative;
    padding: 1rem;
    overflow-x: auto;
}

body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: -1;
}

.container {
    max-width: 1800px;
    width: 100%;
    z-index: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.header {
    text-align: center;
    margin-bottom: 1rem;
}

.logo {
    margin-bottom: 0.5rem;
}

.valorant-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--valorant-red);
    letter-spacing: 3px;
    text-shadow: 0 0 10px rgba(255, 70, 85, 0.5);
}

h1 {
    font-size: 2rem;
    font-weight: 700;
    letter-spacing: 2px;
    margin-bottom: 0;
    text-shadow: 0 0 10px rgba(255, 70, 85, 0.7);
}

.main-content {
    display: flex;
    gap: 2rem;
    align-items: flex-start;
}

.lobby-container {
    background: rgba(30, 58, 95, 0.3);
    border: 2px solid var(--valorant-teal);
    border-radius: 8px;
    padding: 1.5rem;
    flex: 1;
    max-width: 1200px;
}

.game-slots {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1.5rem;
}

.team-section {
    background: rgba(15, 25, 35, 0.6);
    border: 1px solid var(--valorant-teal);
    border-radius: 6px;
    padding: 1rem;
}

.team-title {
    font-size: 1.2rem;
    font-weight: 700;
    letter-spacing: 1px;
    margin-bottom: 0.8rem;
    color: var(--valorant-light);
    text-transform: uppercase;
}

.attackers .team-title {
    color: #ff9500;
}

.defenders .team-title {
    color: #00d4aa;
}

.observers .team-title {
    color: #888;
}

.slots-grid {
    display: flex;
    flex-direction: column;
    gap: 0.4rem;
}

.observers-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.4rem;
}

.slot {
    background: rgba(30, 58, 95, 0.4);
    border: 2px solid var(--valorant-teal);
    border-radius: 4px;
    padding: 0.8rem;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.slot:hover {
    border-color: var(--valorant-red);
    background: rgba(255, 70, 85, 0.1);
}

.slot.selected {
    border-color: var(--valorant-selected);
    background: rgba(0, 212, 170, 0.2);
    box-shadow: 0 0 15px rgba(0, 212, 170, 0.3);
}

.slot.selected::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 30%, rgba(0, 212, 170, 0.1) 50%, transparent 70%);
    animation: shimmer 2s infinite;
}

.slot.afk {
    border-color: var(--valorant-afk);
    background: var(--valorant-afk-bg);
    opacity: 0.6;
    cursor: not-allowed;
}

.slot.afk:hover {
    border-color: var(--valorant-afk);
    background: var(--valorant-afk-bg);
}

.slot.afk .player-name {
    color: var(--valorant-afk);
    text-decoration: line-through;
}

.slot.afk .slot-status {
    color: var(--valorant-afk);
    font-weight: bold;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.slot-content {
    position: relative;
    z-index: 2;
}

.player-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--valorant-light);
    margin-bottom: 0.2rem;
}

.slot-status {
    font-size: 0.8rem;
    color: #888;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.slot.selected .slot-status {
    color: var(--valorant-selected);
    font-weight: 600;
}

.coach-section {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--valorant-teal);
}

.coach-title {
    font-size: 1.1rem;
    font-weight: 600;
    letter-spacing: 1px;
    margin-bottom: 0.8rem;
    color: var(--valorant-light);
    text-transform: uppercase;
}

.attackers .coach-title {
    color: #ff9500;
}

.defenders .coach-title {
    color: #00d4aa;
}

/* Sidebar Styles */
.sidebar {
    width: 320px;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    flex-shrink: 0;
}

.controls-section,
.shortcuts-section,
.info-section {
    background: rgba(30, 58, 95, 0.3);
    border: 2px solid var(--valorant-teal);
    border-radius: 8px;
    padding: 1.5rem;
}

.section-title {
    font-size: 1.2rem;
    font-weight: 700;
    letter-spacing: 1px;
    margin-bottom: 1rem;
    color: var(--valorant-red);
    text-transform: uppercase;
    text-align: center;
}

.controls {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    margin-bottom: 1rem;
}

.val-btn {
    background-color: transparent;
    color: var(--valorant-light);
    border: 2px solid var(--valorant-red);
    padding: 0.8rem 1rem;
    font-family: 'Oswald', sans-serif;
    font-size: 1rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 4px;
    width: 100%;
}

.val-btn:hover {
    background-color: var(--valorant-red);
    border-color: var(--valorant-red);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 70, 85, 0.3);
}

.val-btn.secondary {
    border-color: var(--valorant-teal);
}

.val-btn.secondary:hover {
    background-color: var(--valorant-teal);
    border-color: var(--valorant-teal);
}

/* Shortcuts Section */
.shortcuts-list {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.shortcut-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.6rem;
    background: rgba(15, 25, 35, 0.6);
    border: 1px solid var(--valorant-teal);
    border-radius: 4px;
}

.shortcut-key {
    font-weight: 700;
    color: var(--valorant-selected);
    font-size: 0.9rem;
    letter-spacing: 1px;
}

.shortcut-desc {
    color: var(--valorant-light);
    font-size: 0.9rem;
}

/* Selection Info */
.selection-info {
    text-align: center;
    padding: 1rem;
    background: rgba(15, 25, 35, 0.6);
    border: 1px solid var(--valorant-teal);
    border-radius: 4px;
}

.selection-info p {
    margin: 0;
    font-size: 1rem;
    font-weight: 500;
    color: var(--valorant-light);
}

.links {
    margin: 1rem 0 2rem;
    text-align: center;
}

.switch-version {
    color: var(--valorant-light);
    text-decoration: none;
    font-size: 1rem;
    transition: color 0.2s ease;
    border-bottom: 1px solid var(--valorant-light);
    padding-bottom: 2px;
}

.switch-version:hover {
    color: var(--valorant-red);
    border-color: var(--valorant-red);
}

.creator-info {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 70, 85, 0.3);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.creator-info p {
    font-size: 1.2rem;
    color: var(--valorant-light);
}

.creator-info a {
    color: var(--valorant-red);
    text-decoration: none;
    transition: all 0.3s ease;
}

.creator-info a:hover {
    text-shadow: 0 0 8px rgba(255, 70, 85, 0.8);
}

.youtube-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: var(--valorant-red);
    color: var(--valorant-light) !important;
    padding: 0.6rem 1.2rem;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.youtube-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(255, 70, 85, 0.5);
    text-shadow: none !important;
}

.youtube-link svg {
    width: 20px;
    height: 20px;
}

/* Responsive Design */
@media (max-width: 1600px) {
    .main-content {
        flex-direction: column;
        gap: 1.5rem;
    }

    .sidebar {
        width: 100%;
        max-width: 800px;
        margin: 0 auto;
    }

    .controls-section,
    .shortcuts-section,
    .info-section {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .shortcuts-section .shortcuts-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 0.8rem;
    }
}

@media (max-width: 1200px) {
    .game-slots {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .observers-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .valorant-title {
        font-size: 2rem;
    }

    h1 {
        font-size: 1.5rem;
    }

    .lobby-container {
        padding: 1rem;
    }

    .observers-grid {
        grid-template-columns: 1fr 1fr;
    }

    .val-btn {
        padding: 0.8rem;
        font-size: 0.9rem;
    }

    .sidebar {
        gap: 1rem;
    }

    .controls-section,
    .shortcuts-section,
    .info-section {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0.5rem;
    }

    .observers-grid {
        grid-template-columns: 1fr;
    }

    .slot {
        padding: 0.6rem;
    }

    .player-name {
        font-size: 0.9rem;
    }

    .slot-status {
        font-size: 0.7rem;
    }

    .shortcuts-section .shortcuts-list {
        grid-template-columns: 1fr;
    }
}