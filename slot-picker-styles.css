:root {
    --valorant-red: #ff4655;
    --valorant-dark: #111;
    --valorant-light: #ece8e1;
    --valorant-blue: #0f1923;
    --valorant-teal: #1e3a5f;
    --valorant-selected: #00d4aa;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON>', sans-serif;
    background-color: var(--valorant-blue);
    color: var(--valorant-light);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    padding: 2rem 0;
}

body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: -1;
}

.container {
    text-align: center;
    padding: 2rem;
    max-width: 1400px;
    width: 100%;
    z-index: 1;
}

.logo {
    margin-bottom: 1rem;
}

.valorant-title {
    font-size: 4rem;
    font-weight: 700;
    color: var(--valorant-red);
    letter-spacing: 5px;
    text-shadow: 0 0 10px rgba(255, 70, 85, 0.5);
}

h1 {
    font-size: 3rem;
    font-weight: 700;
    letter-spacing: 2px;
    margin-bottom: 2rem;
    text-shadow: 0 0 10px rgba(255, 70, 85, 0.7);
}

.lobby-container {
    background: rgba(30, 58, 95, 0.3);
    border: 2px solid var(--valorant-teal);
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.game-slots {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.team-section {
    background: rgba(15, 25, 35, 0.6);
    border: 1px solid var(--valorant-teal);
    border-radius: 6px;
    padding: 1.5rem;
}

.team-title {
    font-size: 1.5rem;
    font-weight: 700;
    letter-spacing: 2px;
    margin-bottom: 1rem;
    color: var(--valorant-light);
    text-transform: uppercase;
}

.attackers .team-title {
    color: #ff9500;
}

.defenders .team-title {
    color: #00d4aa;
}

.observers .team-title {
    color: #888;
}

.slots-grid {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.observers-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

.slot {
    background: rgba(30, 58, 95, 0.4);
    border: 2px solid var(--valorant-teal);
    border-radius: 4px;
    padding: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.slot:hover {
    border-color: var(--valorant-red);
    background: rgba(255, 70, 85, 0.1);
}

.slot.selected {
    border-color: var(--valorant-selected);
    background: rgba(0, 212, 170, 0.2);
    box-shadow: 0 0 15px rgba(0, 212, 170, 0.3);
}

.slot.selected::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 30%, rgba(0, 212, 170, 0.1) 50%, transparent 70%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.slot-content {
    position: relative;
    z-index: 2;
}

.player-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--valorant-light);
    margin-bottom: 0.3rem;
}

.slot-status {
    font-size: 0.9rem;
    color: #888;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.slot.selected .slot-status {
    color: var(--valorant-selected);
    font-weight: 600;
}

.coach-section {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--valorant-teal);
}

.coach-title {
    font-size: 1.1rem;
    font-weight: 600;
    letter-spacing: 1px;
    margin-bottom: 0.8rem;
    color: var(--valorant-light);
    text-transform: uppercase;
}

.attackers .coach-title {
    color: #ff9500;
}

.defenders .coach-title {
    color: #00d4aa;
}

.controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.val-btn {
    background-color: transparent;
    color: var(--valorant-light);
    border: 2px solid var(--valorant-red);
    padding: 1rem 2rem;
    font-family: 'Oswald', sans-serif;
    font-size: 1.2rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 4px;
}

.val-btn:hover {
    background-color: var(--valorant-red);
    border-color: var(--valorant-red);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 70, 85, 0.3);
}

.val-btn.secondary {
    border-color: var(--valorant-teal);
}

.val-btn.secondary:hover {
    background-color: var(--valorant-teal);
    border-color: var(--valorant-teal);
}

.selection-info {
    margin-bottom: 2rem;
}

.selection-info p {
    font-size: 1.3rem;
    font-weight: 500;
    color: var(--valorant-light);
}

.links {
    margin: 1rem 0 2rem;
    text-align: center;
}

.switch-version {
    color: var(--valorant-light);
    text-decoration: none;
    font-size: 1rem;
    transition: color 0.2s ease;
    border-bottom: 1px solid var(--valorant-light);
    padding-bottom: 2px;
}

.switch-version:hover {
    color: var(--valorant-red);
    border-color: var(--valorant-red);
}

.creator-info {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 70, 85, 0.3);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.creator-info p {
    font-size: 1.2rem;
    color: var(--valorant-light);
}

.creator-info a {
    color: var(--valorant-red);
    text-decoration: none;
    transition: all 0.3s ease;
}

.creator-info a:hover {
    text-shadow: 0 0 8px rgba(255, 70, 85, 0.8);
}

.youtube-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: var(--valorant-red);
    color: var(--valorant-light) !important;
    padding: 0.6rem 1.2rem;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.youtube-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(255, 70, 85, 0.5);
    text-shadow: none !important;
}

.youtube-link svg {
    width: 20px;
    height: 20px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .game-slots {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .observers-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .valorant-title {
        font-size: 3rem;
    }

    h1 {
        font-size: 2rem;
    }

    .lobby-container {
        padding: 1rem;
    }

    .observers-grid {
        grid-template-columns: 1fr 1fr;
    }

    .val-btn {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }

    .controls {
        flex-direction: column;
    }

    .val-btn {
        width: 100%;
        max-width: 300px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 1rem;
    }

    .observers-grid {
        grid-template-columns: 1fr;
    }

    .slot {
        padding: 0.8rem;
    }

    .player-name {
        font-size: 1rem;
    }

    .slot-status {
        font-size: 0.8rem;
    }
}