# Valorant Random Map Picker

A sleek, Valorant-themed web application that randomly selects maps from the popular FPS game Valorant. Created by [<PERSON><PERSON><PERSON>](https://www.youtube.com/@Shushie_valorant).

## Features

- Choose from standard maps, team deathmatch maps, or all maps
- Valorant-inspired design with the game's color scheme and styling
- Responsive layout that works on desktop and mobile devices
- Smooth animations when selecting a new map
- SEO optimized for better visibility

## Available Maps

### Standard Maps
- Ascent
- Bind
- Haven
- Split
- Icebox
- Breeze
- Fracture
- Pearl
- Lotus
- Sunset
- Abyss

### Team Deathmatch Maps
- Kasbah
- Piazza
- District
- Drift
- Glitch

## How to Use

1. Open `index.html` in any modern web browser
2. Click one of the three buttons:
   - **STANDARD MAPS**: Picks from the standard competitive/unrated map pool
   - **TEAM DEATHMATCH MAPS**: Picks from TDM-specific maps
   - **ALL MAPS**: Picks from any map in the game

## Setup

This is a static website that runs entirely in the browser. No server or installation is required.

### Local Development

To run this locally:

1. <PERSON>lone or download this repository
2. Open `index.html` in your web browser

## SEO Information

This website includes:
- Meta tags for search engines
- Schema.org structured data
- Open Graph tags for social sharing
- Proper heading hierarchy
- Descriptive alt text for images

## Creator

Created by Shushie - [YouTube Channel](https://www.youtube.com/@Shushie_valorant)

## Image Credits

- Valorant map images are property of Riot Games
- This is a fan project and not affiliated with Riot Games

## Technical Details

Built with:
- HTML5
- CSS3 (with animations and responsive design)
- Vanilla JavaScript (no frameworks) 