<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Valorant Random Map Picker | Choose Your Next Map</title>
    <meta name="description" content="Free Valorant random map picker tool. Select from standard maps, TDM maps, or all maps with a single click. Created by <PERSON><PERSON><PERSON>.">
    <meta name="keywords" content="valorant, valorant maps, random map picker, valorant random map, valorant tool, gaming tool, map selector, valorant map randomizer">
    <meta name="author" content="Shushie">
    <meta property="og:title" content="Valorant Random Map Picker">
    <meta property="og:description" content="Free tool to randomly select your next Valorant map. Easy to use and includes all current maps.">
    <meta property="og:type" content="website">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://yourwebsite.com/valorant-map-picker">
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Oswald:wght@400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="logo">
            <h1 class="valorant-title">VALORANT</h1>
        </div>
        <h1>RANDOM MAP PICKER</h1>
        
        <div class="map-container">
            <div class="map-display">
                <img id="map-image" src="Standard Maps/abyss.png" alt="Valorant Map">
                <div id="map-name">SELECT A MODE</div>
            </div>
        </div>
        
        <div class="buttons">
            <button id="standard-btn" class="val-btn">STANDARD MAPS</button>
            <button id="tdm-btn" class="val-btn">TEAM DEATHMATCH MAPS</button>
            <button id="all-btn" class="val-btn">ALL MAPS</button>
        </div>
        
        <div class="links">
            <a href="index2.html" class="switch-version">Try the Spinner Version!</a>
            <a href="index3.html" class="switch-version">Try the Roulette Version!</a>
        </div>
        
        <footer class="creator-info">
            <p>Created by <a href="https://www.youtube.com/@Shushie_valorant" target="_blank" rel="noopener noreferrer">Shushie</a></p>
            <a href="https://www.youtube.com/@Shushie_valorant" target="_blank" rel="noopener noreferrer" class="youtube-link">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/>
                </svg>
                Subscribe on YouTube
            </a>
        </footer>
    </div>
    
    <script src="script.js"></script>
    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": "Valorant Random Map Picker",
            "description": "Free tool to randomly select your next Valorant map. Easy to use and includes all current maps.",
            "author": {
                "@type": "Person",
                "name": "Shushie",
                "url": "https://www.youtube.com/@Shushie_valorant"
            },
            "applicationCategory": "Gaming",
            "offers": {
                "@type": "Offer",
                "price": "0",
                "priceCurrency": "USD"
            }
        }
    </script>
</body>
</html> 