/**
 * Valorant Random Map Picker
 * Created by <PERSON><PERSON><PERSON> (https://www.youtube.com/@Shu<PERSON><PERSON>_valorant)
 * 
 * A tool for randomly selecting Valorant maps for your next match.
 * Includes all standard maps and team deathmatch maps.
 */

document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const mapImage = document.getElementById('map-image');
    const mapName = document.getElementById('map-name');
    const standardBtn = document.getElementById('standard-btn');
    const tdmBtn = document.getElementById('tdm-btn');
    const allBtn = document.getElementById('all-btn');
    
    // Map Data
    const standardMaps = [
        {
            name: 'Ascent',
            image: 'Standard Maps/Ascent.webp'
        },
        {
            name: 'Bind',
            image: 'Standard Maps/Bind.webp'
        },
        {
            name: 'Haven',
            image: 'Standard Maps/Haven.webp'
        },
        {
            name: 'Split',
            image: 'Standard Maps/Split.webp'
        },
        {
            name: 'Icebox',
            image: 'Standard Maps/Icebox.webp'
        },
        {
            name: 'Breeze',
            image: 'Standard Maps/Breeze.webp'
        },
        {
            name: 'Fracture',
            image: 'Standard Maps/Fracture.webp'
        },
        {
            name: 'Pearl',
            image: 'Standard Maps/Pearl.webp'
        },
        {
            name: 'Lotus',
            image: 'Standard Maps/Lotus.webp'
        },
        {
            name: 'Sunset',
            image: 'Standard Maps/Sunset.webp'
        },
        {
            name: 'Abyss',
            image: 'Standard Maps/abyss.png'
        }
    ];
    
    const tdmMaps = [
        {
            name: 'Kasbah',
            image: 'TDM Maps/Kasbah.jpg'
        },
        {
            name: 'Piazza',
            image: 'TDM Maps/Piazza.jpg'
        },
        {
            name: 'District',
            image: 'TDM Maps/District.jpg'
        },
        {
            name: 'Drift',
            image: 'TDM Maps/Drift.jpg'
        },
        {
            name: 'Glitch',
            image: 'TDM Maps/Glitch.jpg'
        }
    ];
    
    // Combine all maps
    const allMaps = [...standardMaps, ...tdmMaps];

    /**
     * Caches an image to localStorage as base64 data
     * @param {string} src - Image source URL
     * @param {string} mapName - Name of the map for cache key
     */
    function cacheImage(src, mapName) {
        // Check if we already have this image cached
        if (localStorage.getItem(`map_${mapName}`)) {
            return;
        }

        // Fetch the image and convert to base64
        fetch(src)
            .then(response => response.blob())
            .then(blob => {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onloadend = () => resolve(reader.result);
                    reader.onerror = reject;
                    reader.readAsDataURL(blob);
                });
            })
            .then(base64Data => {
                try {
                    localStorage.setItem(`map_${mapName}`, base64Data);
                    console.log(`Cached ${mapName} image`);
                } catch (error) {
                    // Handle localStorage quota exceeded
                    console.warn(`Failed to cache ${mapName}: ${error.message}`);
                    // If quota exceeded, remove oldest items
                    if (error.name === 'QuotaExceededError') {
                        clearOldestCache();
                        // Try again
                        localStorage.setItem(`map_${mapName}`, base64Data);
                    }
                }
            })
            .catch(error => {
                console.error(`Error caching image ${mapName}:`, error);
            });
    }

    /**
     * Removes the oldest cached images if storage is full
     */
    function clearOldestCache() {
        // Get all map cache keys
        const cacheKeys = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key.startsWith('map_')) {
                cacheKeys.push(key);
            }
        }

        // If we have cached items, remove the first one (oldest)
        if (cacheKeys.length > 0) {
            localStorage.removeItem(cacheKeys[0]);
            console.log(`Removed oldest cache: ${cacheKeys[0]}`);
        }
    }

    /**
     * Gets an image from cache or returns the original source
     * @param {string} src - Original image source URL
     * @param {string} mapName - Name of the map for cache key
     * @returns {string} - Image source (either from cache or original)
     */
    function getImageSource(src, mapName) {
        const cachedImage = localStorage.getItem(`map_${mapName}`);
        if (cachedImage) {
            return cachedImage;
        }
        
        // If not cached, we'll use the original source and cache it
        cacheImage(src, mapName);
        return src;
    }
    
    /**
     * Selects a random map from the provided array and displays it
     * with a smooth animation effect
     * @param {Array} maps - Array of map objects with name and image properties
     */
    function pickRandomMap(maps) {
        // Reset animation
        mapImage.classList.remove('map-change');
        
        // Wait a brief moment to ensure animation reset
        setTimeout(() => {
            // Pick random map
            const randomIndex = Math.floor(Math.random() * maps.length);
            const selectedMap = maps[randomIndex];
            
            // Get image source (from cache if available)
            const imageSource = getImageSource(selectedMap.image, selectedMap.name);
            
            // Update UI with animation
            mapImage.classList.add('map-change');
            mapImage.src = imageSource;
            mapName.textContent = selectedMap.name;
            
            // Update page title for better SEO
            document.title = `${selectedMap.name} - Valorant Random Map Picker | by Shushie`;
            
            // Add the selected map as a URL parameter without page reload (for sharing)
            const url = new URL(window.location);
            url.searchParams.set('map', selectedMap.name);
            window.history.replaceState({}, '', url);
        }, 50);
    }
    
    // Event Listeners
    standardBtn.addEventListener('click', () => pickRandomMap(standardMaps));
    tdmBtn.addEventListener('click', () => pickRandomMap(tdmMaps));
    allBtn.addEventListener('click', () => pickRandomMap(allMaps));
    
    // Fallback for missing images
    mapImage.addEventListener('error', () => {
        mapImage.src = 'Standard Maps/abyss.png';
    });
    
    // Check URL for map parameter on load
    const urlParams = new URLSearchParams(window.location.search);
    const mapParam = urlParams.get('map');
    if (mapParam) {
        // Find the map in our arrays
        const allMapsLookup = allMaps.reduce((acc, map) => {
            acc[map.name.toLowerCase()] = map;
            return acc;
        }, {});
        
        const requestedMap = allMapsLookup[mapParam.toLowerCase()];
        if (requestedMap) {
            // Get image source (from cache if available)
            const imageSource = getImageSource(requestedMap.image, requestedMap.name);
            
            // Display the requested map
            mapImage.src = imageSource;
            mapName.textContent = requestedMap.name;
            document.title = `${requestedMap.name} - Valorant Random Map Picker | by Shushie`;
        }
    }
}); 