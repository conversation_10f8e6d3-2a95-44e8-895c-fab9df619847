/**
 * Valorant Random Slot Picker
 * Created by <PERSON><PERSON><PERSON> (https://www.youtube.com/@Shushie_valorant)
 *
 * A tool for randomly selecting 10 players from 22 available slots for Valorant custom matches.
 * Includes all player slots: Attackers, Defenders, Observers, and Coaches.
 */

document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const pickRandomBtn = document.getElementById('pick-random-btn');
    const resetBtn = document.getElementById('reset-btn');
    const selectionCount = document.getElementById('selection-count');
    const allSlots = document.querySelectorAll('.slot');

    // Game state
    let selectedSlots = new Set();
    let afkSlots = new Set();
    const totalSlots = 22;
    const playersToSelect = 10;

    /**
     * Updates the selection count display
     */
    function updateSelectionCount() {
        const count = selectedSlots.size;
        const afkCount = afkSlots.size;
        const availablePlayers = totalSlots - afkCount;

        if (count === 0) {
            if (afkCount > 0) {
                selectionCount.textContent = `Select ${playersToSelect} players to start the match (${availablePlayers} available, ${afkCount} AFK)`;
            } else {
                selectionCount.textContent = `Select ${playersToSelect} players to start the match`;
            }
            selectionCount.style.color = 'var(--valorant-light)';
        } else if (count === playersToSelect) {
            selectionCount.textContent = `✓ ${count} players selected - Ready to play!`;
            selectionCount.style.color = 'var(--valorant-selected)';
        } else {
            if (afkCount > 0) {
                selectionCount.textContent = `${count}/${playersToSelect} players selected (${availablePlayers} available, ${afkCount} AFK)`;
            } else {
                selectionCount.textContent = `${count}/${playersToSelect} players selected`;
            }
            selectionCount.style.color = 'var(--valorant-red)';
        }
    }

    /**
     * Clears all current selections
     */
    function clearSelections() {
        selectedSlots.clear();
        allSlots.forEach(slot => {
            slot.classList.remove('selected');
            const status = slot.querySelector('.slot-status');
            if (!afkSlots.has(parseInt(slot.dataset.slot))) {
                status.textContent = 'Ready';
            }
        });
        updateSelectionCount();
    }

    /**
     * Clears all AFK status
     */
    function clearAfkStatus() {
        afkSlots.clear();
        allSlots.forEach(slot => {
            slot.classList.remove('afk');
            const status = slot.querySelector('.slot-status');
            if (!selectedSlots.has(parseInt(slot.dataset.slot))) {
                status.textContent = 'Ready';
            }
        });
        updateSelectionCount();
    }

    /**
     * Toggles AFK status for a slot
     * @param {HTMLElement} slot - The slot element
     */
    function toggleAfkStatus(slot) {
        const slotNumber = parseInt(slot.dataset.slot);
        const status = slot.querySelector('.slot-status');

        if (afkSlots.has(slotNumber)) {
            // Remove from AFK
            afkSlots.delete(slotNumber);
            slot.classList.remove('afk');
            if (selectedSlots.has(slotNumber)) {
                status.textContent = 'Selected';
            } else {
                status.textContent = 'Ready';
            }
        } else {
            // Add to AFK (and remove from selected if needed)
            afkSlots.add(slotNumber);
            selectedSlots.delete(slotNumber);
            slot.classList.remove('selected');
            slot.classList.add('afk');
            status.textContent = 'AFK';
        }

        updateSelectionCount();
    }

    /**
     * Adds selection animation to a slot
     * @param {HTMLElement} slot - The slot element to animate
     * @param {number} delay - Animation delay in milliseconds
     */
    function animateSlotSelection(slot, delay = 0) {
        setTimeout(() => {
            slot.classList.add('selected');
            const status = slot.querySelector('.slot-status');
            status.textContent = 'Selected';

            // Add a brief highlight effect
            slot.style.transform = 'scale(1.05)';
            setTimeout(() => {
                slot.style.transform = 'scale(1)';
            }, 200);
        }, delay);
    }

    /**
     * Picks random slots with animation
     */
    function pickRandomSlots() {
        // Clear previous selections
        clearSelections();

        // Disable button during animation
        pickRandomBtn.disabled = true;
        pickRandomBtn.textContent = 'SELECTING...';

        // Create array of available slot indices (excluding AFK players)
        const availableSlotIndices = [];
        for (let i = 0; i < totalSlots; i++) {
            const slotNumber = parseInt(allSlots[i].dataset.slot);
            if (!afkSlots.has(slotNumber)) {
                availableSlotIndices.push(i);
            }
        }

        // Check if we have enough available players
        if (availableSlotIndices.length < playersToSelect) {
            pickRandomBtn.disabled = false;
            pickRandomBtn.textContent = 'PICK RANDOM 10 PLAYERS';
            alert(`Not enough available players! Need ${playersToSelect} players but only ${availableSlotIndices.length} are available (${afkSlots.size} are AFK).`);
            return;
        }

        // Shuffle the available slots using Fisher-Yates algorithm
        for (let i = availableSlotIndices.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [availableSlotIndices[i], availableSlotIndices[j]] = [availableSlotIndices[j], availableSlotIndices[i]];
        }

        // Select first 10 slots from shuffled available array
        const selectedIndices = availableSlotIndices.slice(0, playersToSelect);

        // Sort selected indices for better visual flow
        selectedIndices.sort((a, b) => a - b);

        // Animate selections with staggered timing
        selectedIndices.forEach((slotIndex, animationIndex) => {
            const slot = allSlots[slotIndex];
            const slotNumber = parseInt(slot.dataset.slot);
            selectedSlots.add(slotNumber);

            // Stagger animations for visual effect
            animateSlotSelection(slot, animationIndex * 150);
        });

        // Update UI after all animations
        setTimeout(() => {
            updateSelectionCount();
            pickRandomBtn.disabled = false;
            pickRandomBtn.textContent = 'PICK RANDOM 10 PLAYERS';

            // Update page title for better UX
            document.title = `${selectedSlots.size} Players Selected - Valorant Random Slot Picker | by Shushie`;

            // Add selected slots as URL parameter for sharing
            const selectedArray = Array.from(selectedSlots).sort((a, b) => a - b);
            const url = new URL(window.location);
            url.searchParams.set('selected', selectedArray.join(','));
            window.history.replaceState({}, '', url);

        }, selectedIndices.length * 150 + 200);
    }

    /**
     * Handles manual slot clicking with AFK support
     * @param {HTMLElement} slot - The clicked slot
     * @param {Event} event - The click event
     */
    function handleSlotClick(slot, event) {
        const slotNumber = parseInt(slot.dataset.slot);

        // Alt+Click toggles AFK status
        if (event && event.altKey) {
            toggleAfkStatus(slot);
            return;
        }

        // Don't allow selection of AFK players
        if (afkSlots.has(slotNumber)) {
            return;
        }

        if (selectedSlots.has(slotNumber)) {
            // Deselect slot
            selectedSlots.delete(slotNumber);
            slot.classList.remove('selected');
            slot.querySelector('.slot-status').textContent = 'Ready';
        } else if (selectedSlots.size < playersToSelect) {
            // Select slot if under limit
            selectedSlots.add(slotNumber);
            animateSlotSelection(slot);
        }

        updateSelectionCount();
    }

    /**
     * Loads selection from URL parameters
     */
    function loadFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        const selectedParam = urlParams.get('selected');

        if (selectedParam) {
            const selectedArray = selectedParam.split(',').map(num => parseInt(num)).filter(num => !isNaN(num) && num >= 1 && num <= totalSlots);

            if (selectedArray.length <= playersToSelect) {
                clearSelections();

                selectedArray.forEach((slotNumber, index) => {
                    const slot = document.querySelector(`[data-slot="${slotNumber}"]`);
                    if (slot) {
                        selectedSlots.add(slotNumber);
                        animateSlotSelection(slot, index * 100);
                    }
                });

                setTimeout(() => {
                    updateSelectionCount();
                }, selectedArray.length * 100 + 200);
            }
        }
    }

    // Event Listeners
    pickRandomBtn.addEventListener('click', pickRandomSlots);
    resetBtn.addEventListener('click', clearSelections);

    // Add click handlers to all slots for manual selection and AFK toggle
    allSlots.forEach(slot => {
        slot.addEventListener('click', (event) => handleSlotClick(slot, event));
    });

    // Add clear AFK button functionality
    const clearAfkBtn = document.getElementById('clear-afk-btn');
    if (clearAfkBtn) {
        clearAfkBtn.addEventListener('click', clearAfkStatus);
    }

    // Add keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        if (e.code === 'Space' && !pickRandomBtn.disabled) {
            e.preventDefault();
            pickRandomSlots();
        } else if (e.code === 'Escape') {
            clearSelections();
        }
    });

    // Initialize
    updateSelectionCount();
    loadFromURL();

    // Add some visual feedback for keyboard shortcuts
    const shortcutInfo = document.createElement('div');
    shortcutInfo.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: rgba(15, 25, 35, 0.9);
        color: var(--valorant-light);
        padding: 10px;
        border-radius: 4px;
        font-size: 0.8rem;
        border: 1px solid var(--valorant-teal);
        z-index: 1000;
    `;
    shortcutInfo.innerHTML = `
        <div>Controls:</div>
        <div>SPACE - Random Pick</div>
        <div>ESC - Reset Selection</div>
        <div>ALT+Click - Toggle AFK</div>
    `;
    document.body.appendChild(shortcutInfo);
});